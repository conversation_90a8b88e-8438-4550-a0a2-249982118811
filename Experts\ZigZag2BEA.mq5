//+------------------------------------------------------------------+
//|                                      ZigZag2BEA.mq5                |
//|                        Copyright 2024, QuantResearch             |
//|                                       https://quantresearch.org  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, QuantResearch"
#property link      "https://quantresearch.org"
#property version   "1.00"
#property strict

//--- 输入参数
input int ZigZagDepth    = 1;       // ZigZag深度（%）
input int ZigZagDeviation= 1;       // ZigZag偏差（%）
input int ZigZagBackstep = 0;       // ZigZag回退步数
input bool KeepHistory   = true;    // 保留历史标记
input color UpColor      = clrDodgerBlue;  // 上升趋势颜色
input color DnColor      = clrRed;       // 下降趋势颜色
input int LineWidth      = 2;         // 线宽
input int ArrowSize      = 3;         // 箭头大小

//--- 全局变量
int zigzagHandle;                     // ZigZag指标句柄
datetime lastProcessedBar = 0;        // 上次处理的K线时间
int zigzagPointsCount = 0;            // ZigZag点数量

// 形态状态枚举
enum FormationType { NONE, UP_FORMING, DN_FORMING };
FormationType currentFormation = NONE;

// 形态状态结构体（上升趋势）
struct UpFormation {
    datetime A_time;       // A点时间（高点）
    double A_price;        // A点价格
    datetime L2_start;     // L2趋势线起点时间
    double L2_start_p;     // L2趋势线起点价格
    datetime L2_end;       // L2趋势线终点时间
    double L2_end_p;       // L2趋势线终点价格
    datetime line_start;   // 横线起始时间（=A_time）
    double line_price;     // 横线价格（=A_price）
    datetime intersect1;   // 与L2的交点时间（初始为0）
    double intersect1_p;   // 与L2的交点价格（初始为0）
    datetime intersect2;   // 跌破交点时间（初始为0）
    double intersect2_p;   // 跌破交点价格（初始为0）
    bool L4_active;        // L4是否激活（生成中）
};
UpFormation upForm;

// 形态状态结构体（下降趋势）
struct DnFormation {
    datetime A_time;       // A点时间（低点）
    double A_price;        // A点价格
    datetime L2_start;     // L2趋势线起点时间
    double L2_start_p;     // L2趋势线起点价格
    datetime L2_end;       // L2趋势线终点时间
    double L2_end_p;       // L2趋势线终点价格
    datetime line_start;   // 横线起始时间（=A_time）
    double line_price;     // 横线价格（=A_price）
    datetime intersect1;   // 与L2的交点时间（初始为0）
    double intersect1_p;   // 与L2的交点价格（初始为0）
    datetime intersect2;   // 突破交点时间（初始为0）
    double intersect2_p;   // 突破交点价格（初始为0）
    bool L4_active;        // L4是否激活（生成中）
};
DnFormation dnForm;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // 初始化ZigZag指标句柄
    zigzagHandle = iZigZag(_Symbol, _Period, ZigZagDepth, ZigZagDeviation, ZigZagBackstep);
    if(zigzagHandle == INVALID_HANDLE) {
        Print("错误：无法创建ZigZag指标句柄");
        return(INIT_FAILED);
    }

    // 设置对象名称前缀
    ObjectSetString(0, "Prefix", OBJPROP_PREFIX, "ZigZag2B_");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 删除所有图形对象
    ObjectsDeleteAll(0, "ZigZag2B_");
    
    // 释放指标句柄
    IndicatorRelease(zigzagHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // 仅处理新形成的K线
    if(TimeCurrent() == lastProcessedBar) return;
    lastProcessedBar = TimeCurrent();

    // 更新ZigZag点数据
    if(!UpdateZigZagPoints()) return;

    // 检测形态
    CheckFormations();

    // 绘制图形
    DrawObjects();
}

//+------------------------------------------------------------------+
//| 更新ZigZag转折点数据                                             |
//+------------------------------------------------------------------+
bool UpdateZigZagPoints() {
    // 获取ZigZag趋势缓冲区（0=主趋势，1=深度，2=反向）
    int count = Bars(_Symbol, _Period);
    double trendBuf[];
    ArraySetAsSeries(trendBuf, true);
    
    if(CopyBuffer(zigzagHandle, 0, 0, count, trendBuf) <= 0) {
        Print("错误：无法获取ZigZag趋势数据");
        return false;
    }

    // 清理旧数据并提取有效转折点
    zigzagPointsCount = 0;
    for(int i=0; i<count; i++) {
        if(trendBuf[i] != 0) zigzagPointsCount++;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 检测2B形态                                                       |
//+------------------------------------------------------------------+
void CheckFormations() {
    // 仅当ZigZag点足够时检测
    if(zigzagPointsCount < 7) return;

    // 检测上升趋势2B形态
    if(currentFormation != UP_FORMING) {
        if(DetectUpFormation()) {
            currentFormation = UP_FORMING;
            Print("检测到上升2B形态，A点时间：", TimeToString(upForm.A_time));
        }
    }

    // 检测下降趋势2B形态
    if(currentFormation != DN_FORMING) {
        if(DetectDnFormation()) {
            currentFormation = DN_FORMING;
            Print("检测到下降2B形态，A点时间：", TimeToString(dnForm.A_time));
        }
    }

    // 处理形态持续状态
    if(currentFormation == UP_FORMING) ProcessUpFormation();
    if(currentFormation == DN_FORMING) ProcessDnFormation();
}

//+------------------------------------------------------------------+
//| 检测上升2B形态                                                   |
//+------------------------------------------------------------------+
bool DetectUpFormation() {
    // 简化版检测逻辑（实际需根据ZigZag点序列优化）
    // 这里假设已通过ZigZag点找到符合要求的A、L2、B、L3、C点
    // 实际应用中需要遍历ZigZag点验证趋势线条件
    
    // 示例：假设A点为最近的高点，L2为最近的下降趋势线
    // （此处需替换为真实的点检测逻辑）
    upForm.A_time = iTime(_Symbol, _Period, 100);
    upForm.A_price = iHigh(_Symbol, _Period, 100);
    upForm.line_start = upForm.A_time;
    upForm.line_price = upForm.A_price;
    
    return true;
}

//+------------------------------------------------------------------+
//| 检测下降2B形态                                                   |
//+------------------------------------------------------------------+
bool DetectDnFormation() {
    // 示例：假设A点为最近的低点，L2为最近的上升趋势线
    dnForm.A_time = iTime(_Symbol, _Period, 200);
    dnForm.A_price = iLow(_Symbol, _Period, 200);
    dnForm.line_start = dnForm.A_time;
    dnForm.line_price = dnForm.A_price;
    
    return true;
}

//+------------------------------------------------------------------+
//| 处理上升形态持续状态                                             |
//+------------------------------------------------------------------+
void ProcessUpFormation() {
    // 计算与L2的交点
    CalculateIntersection(upForm);

    // 检测跌破事件
    if(Close[0] < upForm.line_price && upForm.intersect2 == 0) {
        upForm.intersect2 = TimeCurrent();
        upForm.intersect2_p = Close[0];
        Print("警告：上升2B形态跌破横线，时间：", TimeToString(upForm.intersect2));
    }
}

//+------------------------------------------------------------------+
//| 处理下降形态持续状态                                             |
//+------------------------------------------------------------------+
void ProcessDnFormation() {
    // 计算与L2的交点
    CalculateIntersection(dnForm);

    // 检测突破事件
    if(Close[0] > dnForm.line_price && dnForm.intersect2 == 0) {
        dnForm.intersect2 = TimeCurrent();
        dnForm.intersect2_p = Close[0];
        Print("警告：下降2B形态突破横线，时间：", TimeToString(dnForm.intersect2));
    }
}

//+------------------------------------------------------------------+
//| 计算横线与趋势线的交点                                           |
//+------------------------------------------------------------------+
void CalculateIntersection(UpFormation &form) {
    // 示例：简化计算（实际需根据L2的起点和终点计算直线方程）
    // 假设L2由两点组成（L2_start和L2_end）
    datetime t1 = form.L2_start;
    double p1 = form.L2_start_p;
    datetime t2 = form.L2_end;
    double p2 = form.L2_end_p;
    
    // 直线方程：p = p1 + (p2-p1)*(t-t1)/(t2-t1)
    // 求p=form.line_price时的t
    double slope = (p2 - p1) / (t2 - t1);
    double delta = (form.line_price - p1) / slope;
    datetime intersect_t = t1 + delta;
    
    // 检查交点是否在L2时间范围内
    if(intersect_t >= t1 && intersect_t <= t2) {
        form.intersect1 = intersect_t;
        form.intersect1_p = form.line_price;
    }
}

//+------------------------------------------------------------------+
//| 绘制所有图形对象                                                 |
//+------------------------------------------------------------------+
void DrawObjects() {
    // 清理旧对象（当不保留历史时）
    if(!KeepHistory) {
        ObjectsDeleteAll(0, "ZigZag2B_");
    }

    // 绘制上升形态
    if(currentFormation == UP_FORMING) {
        DrawHorizontalLine(upForm.line_start, upForm.line_price, upForm.line_price, 
                         "UpLine", UpColor, LINE_STYLE_DOT, LineWidth);
        
        if(upForm.intersect1 != 0) {
            DrawMarker(upForm.intersect1, upForm.intersect1_p, "UpIntersect1", 
                      UpColor, 233, ArrowSize); // 233=向上箭头
        }
        
        if(upForm.intersect2 != 0) {
            DrawMarker(upForm.intersect2, upForm.intersect2_p, "UpIntersect2", 
                      UpColor, 234, ArrowSize); // 234=向下箭头
        }
    }

    // 绘制下降形态
    if(currentFormation == DN_FORMING) {
        DrawHorizontalLine(dnForm.line_start, dnForm.line_price, dnForm.line_price, 
                         "DnLine", DnColor, LINE_STYLE_DOT, LineWidth);
        
        if(dnForm.intersect1 != 0) {
            DrawMarker(dnForm.intersect1, dnForm.intersect1_p, "DnIntersect1", 
                      DnColor, 233, ArrowSize); // 向上箭头
        }
        
        if(dnForm.intersect2 != 0) {
            DrawMarker(dnForm.intersect2, dnForm.intersect2_p, "DnIntersect2", 
                      DnColor, 234, ArrowSize); // 向下箭头
        }
    }
}

//+------------------------------------------------------------------+
//| 绘制水平虚线                                                     |
//+------------------------------------------------------------------+
void DrawHorizontalLine(datetime start, double price, double price2, 
                       string name, color clr, int style, int width) {
    datetime end = TimeCurrent() + PeriodSeconds(); // 延伸至未来
    ObjectCreate(0, name, OBJ_TREND, 0, start, price, end, price2);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_STYLE, style);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
    ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
}

//+------------------------------------------------------------------+
//| 绘制标记（箭头）                                                 |
//+------------------------------------------------------------------+
void DrawMarker(datetime time, double price, string name, color clr, 
               long arrow_code, int size) {
    ObjectCreate(0, name, OBJ_ARROW, 0, time, price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_ARROWCODE, arrow_code);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, size);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}